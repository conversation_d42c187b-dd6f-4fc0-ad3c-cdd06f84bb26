# UE Editor Python script
# Usage: Window -> Developer Tools -> Output Log, then:  py "Content/Python/auto_setup_sora.py"
import unreal

def import_sora_videos_from(folder="/Game/Cinematics", disk_dir="C:/SoraExports"):
    at = unreal.AssetToolsHelpers.get_asset_tools()
    # Ensure folder exists
    unreal.EditorAssetLibrary.make_directory(folder)

    ctx = unreal.AutomatedAssetImportData()
    ctx.set_editor_property("destination_path", folder)
    ctx.set_editor_property("replace_existing", True)
    ctx.set_editor_property("filenames", [])

    # discover mp4s
    import os
    mp4s = []
    for f in os.listdir(disk_dir):
        if f.lower().endswith(".mp4"):
            mp4s.append(os.path.join(disk_dir, f))
    if not mp4s:
        unreal.log_warning("No MP4s found in {}".format(disk_dir)); return

    ctx.set_editor_property("filenames", mp4s)
    assets = at.import_assets_automated(ctx)
    for a in assets:
        unreal.log("Imported: {}".format(a.get_path_name()))

if __name__ == "__main__":
    import_sora_videos_from()
