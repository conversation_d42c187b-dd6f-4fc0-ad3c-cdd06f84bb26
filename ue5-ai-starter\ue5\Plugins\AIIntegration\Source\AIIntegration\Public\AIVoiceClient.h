#pragma once

#include "Kismet/BlueprintFunctionLibrary.h"
#include "AIVoiceClient.generated.h"

DECLARE_DYNAMIC_DELEGATE_TwoParams(FAIHttpResponseDelegate, int32, StatusCode, const FString&, ResponseBody);

/**
 * Minimal HTTP helpers for AI microservices.
 * Blueprint usage:
 *  - PostJson("http://localhost:8787/tts", "{\"text\":\"Hello\"}", {}, OnComplete)
 *  - PostFile("http://localhost:8787/transcribe", FilePath, "audio", {}, OnComplete)
 */
UCLASS()
class AIINTEGRATION_API UAIHttpClient : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    UFUNCTION(BlueprintCallable, Category="AI|HTTP")
    static void PostJson(const FString& URL, const FString& JsonBody, const TMap<FString, FString>& Headers, const FAIHttpResponseDelegate& OnComplete);

    UFUNCTION(BlueprintCallable, Category="AI|HTTP")
    static void PostFile(const FString& URL, const FString& FilePath, const FString& FieldName, const TMap<FString, FString>& Headers, const FAIHttpResponseDelegate& OnComplete);

private:
    static void SendRequest(TSharedRef<class IHttpRequest, ESPMode::ThreadSafe> Request, const FAIHttpResponseDelegate OnComplete);
    static FString BuildMultipartBody(const FString& Boundary, const FString& FieldName, const FString& FilePath, TArray<uint8>& OutBinary);
};
