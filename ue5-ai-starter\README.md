# UE5 + AI Starter (Voice + Cutscenes + Prompts)

This repo proves the concept: **UE5 gameplay** with **AI-generated VO & commands** and **Sora cutscenes**—without you learning deep UE5 internals.

## Contents
- `services/voiceio/` — Node microservice for **STT** (gpt-4o-transcribe/whisper-1) and **TTS** (tts-1).
- `ue5/Plugins/AIIntegration/` — UE5 plugin exposing Blueprint nodes to **POST JSON** or **POST FILE** to your AI service.
- `ue5/Plugins/AIIntegration/Content/Python/auto_setup_sora.py` — Editor helper to bulk-import Sora MP4s.
- `tools/prompts/` — Prompt templates for image gen + Sora.

---

## 1) Run the AI service
```bash
cd services/voiceio
cp .env.example .env   # put your OpenAI key
npm i
npm start   # starts on http://localhost:8787
```

Test:
- `GET /health` → `{ ok: true }`
- `POST /tts` with `{ "text": "Another day, another crater." }` returns a WAV
- `POST /transcribe` with an `audio` file returns `{ "text": "..." }`

> Models: by default uses `gpt-4o-transcribe` (STT) and `tts-1` (TTS). Override in `.env`.

---

## 2) Add the UE5 plugin to your project
1. Create a fresh **UE5 C++ project** (Blank).
2. Copy `ue5/Plugins/AIIntegration` into your project’s `Plugins/` folder.
3. Launch UE → **Edit → Plugins** → ensure **AI Integration** is enabled.
4. In **Project Settings → Plugins → HTTP**, confirm HTTP is enabled (default).

This plugin exposes two Blueprint nodes:
- **PostJson(URL, JsonBody, Headers, OnComplete)**
- **PostFile(URL, FilePath, FieldName, Headers, OnComplete)**

### Example (Blueprint)
- On key press “PushToTalk”:
  1. Record mic to `Saved/Temp/clip.wav` (use *Audio Capture* plugin or a simple recording widget).
  2. Call: `PostFile("http://localhost:8787/transcribe", "C:/path/to/clip.wav", "audio", {}, OnComplete)`
  3. Parse `ResponseBody` JSON → Route commands (`reload`, `grenade`, `shotgun`).
- To speak a line:
  1. `PostJson("http://localhost:8787/tts", "{"text":"Target down"}", {}, OnComplete)`
  2. Save returned bytes to file (optional: add a small Blueprint function to write bytes), or handle on server to pre-generate VO packs.

> Note: Runtime audio import to `USoundWave` requires a small amount of extra code; the sample focuses on HTTP + data flow. Easiest path: generate VO packs as files (offline) and import normally.

---

## 3) Sora cutscenes
- Generate 10–20s clips in Sora (web editor). Export MP4s to a folder (e.g., `C:/SoraExports`).
- In UE Editor: open Output Log → run:  
  `py "Content/Python/auto_setup_sora.py"`  
  This imports all MP4s into `/Game/Cinematics` as Media assets.

Then play via Media Player or place on a mesh as a Media Texture.

---

## 4) Image concepts → textures
- Use `tools/prompts/image_hero_concept.txt` as a starting point for `gpt-image-1`.
- Clean up textures in Photoshop/Krita and create PBR materials in UE.

---

## 5) What’s missing (by design)
- Full gameplay loop. Start with UE First Person template; wire voice commands to actions.
- Runtime WAV → USoundWave conversion (optional enhancement).
- Realtime voice chat (WebSocket). You can add an endpoint in `voiceio` that proxies OpenAI Realtime if needed.

---

## Troubleshooting
- If `PostFile` returns code 0: check file path and that UE can read it.
- CORS: `voiceio` allows all origins via `cors()`. Lock it down for production.
- OpenAI errors: ensure billing, model names, and file size (<25MB) are OK.

---

## Legal/IP
Keep original characters & art. Don’t reuse third-party IP (names, likenesses, logos, catchphrases).

---

## Next Steps
- I can add a **runtime WAV → USoundWave** helper and a **Blueprint node to save bytes to file**, plus sample Player BP for push-to-talk and command routing.
- If you want UE5-only (no Node), I can replace `voiceio` with direct UE→OpenAI calls.
