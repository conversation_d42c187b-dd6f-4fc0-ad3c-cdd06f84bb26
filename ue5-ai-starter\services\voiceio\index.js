import express from "express";
import fileUpload from "express-fileupload";
import cors from "cors";
import fs from "fs";
import OpenAI from "openai";

const app = express();
app.use(cors());
app.use(fileUpload({ limits: { fileSize: 25 * 1024 * 1024 }, useTempFiles: true, tempFileDir: "/tmp" }));
app.use(express.json());

const client = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

app.get("/health", (_, res) => res.json({ ok: true }));

// POST /transcribe  (multipart/form-data with 'audio' file field)
app.post("/transcribe", async (req, res) => {
  try {
    const f = req.files?.audio;
    if (!f) return res.status(400).json({ error: "missing file 'audio'" });
    const tmpPath = Array.isArray(f) ? f[0].tempFilePath : f.tempFilePath;
    const model = process.env.TRANSCRIBE_MODEL || "gpt-4o-transcribe"; // fallback "whisper-1"
    const resp = await client.audio.transcriptions.create({
      model,
      file: fs.createReadStream(tmpPath)
    });
    res.json({ text: resp.text || "" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: err?.message || "transcribe failed" });
  }
});

// POST /tts  (JSON: {text, voice?, format?})
app.post("/tts", async (req, res) => {
  try {
    const text = (req.body?.text || "").toString().trim();
    if (!text) return res.status(400).json({ error: "text required" });
    const voice = req.body?.voice || (process.env.TTS_VOICE || "alloy");
    const format = req.body?.format || "wav"; // mp3|wav|pcm
    const model = process.env.TTS_MODEL || "tts-1"; // or "gpt-4o-mini-tts"
    const speech = await client.audio.speech.create({
      model, voice, input: text, format
    });
    const buf = Buffer.from(await speech.arrayBuffer());
    res.setHeader("Content-Type", `audio/${format}`);
    res.send(buf);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: err?.message || "tts failed" });
  }
});

const port = process.env.PORT || 8787;
app.listen(port, () => console.log(`voiceio up on :${port}`));
