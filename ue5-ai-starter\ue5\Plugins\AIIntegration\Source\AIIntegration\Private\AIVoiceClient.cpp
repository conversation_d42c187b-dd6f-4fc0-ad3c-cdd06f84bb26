\
#include "AIVoiceClient.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"

void UAIHttpClient::PostJson(const FString& URL, const FString& JsonBody, const TMap<FString, FString>& Headers, const FAIHttpResponseDelegate& OnComplete)
{
    auto& Http = FHttpModule::Get();
    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> Request = Http.CreateRequest();
    Request->SetURL(URL);
    Request->SetVerb(TEXT("POST"));
    Request->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
    for (const auto& Kvp : Headers) Request->SetHeader(Kvp.Key, Kvp.Value);
    Request->SetContentAsString(JsonBody);
    SendRequest(Request, OnComplete);
}

void UAIHttpClient::PostFile(const FString& URL, const FString& FilePath, const FString& FieldName, const TMap<FString, FString>& Headers, const FAIHttpResponseDelegate& OnComplete)
{
    if (!FPaths::FileExists(FilePath))
    {
        FAIHttpResponseDelegate Copy = OnComplete;
        // Return 0 status with error json
        Copy.ExecuteIfBound(0, FString::Printf(TEXT("{\"error\":\"file not found: %s\"}"), *FilePath));
        return;
    }

    const FString Boundary = TEXT("----UE5AI") + FString::FromInt(FDateTime::UtcNow().GetTicks());
    TArray<uint8> Body;
    FString ContentType = FString::Printf(TEXT("multipart/form-data; boundary=%s"), *Boundary);

    FString PayloadHeader = BuildMultipartBody(Boundary, FieldName, FilePath, Body);

    auto& Http = FHttpModule::Get();
    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> Request = Http.CreateRequest();
    Request->SetURL(URL);
    Request->SetVerb(TEXT("POST"));
    Request->SetHeader(TEXT("Content-Type"), ContentType);
    for (const auto& Kvp : Headers) Request->SetHeader(Kvp.Key, Kvp.Value);
    Request->SetContent(Body);
    SendRequest(Request, OnComplete);
}

void UAIHttpClient::SendRequest(TSharedRef<IHttpRequest, ESPMode::ThreadSafe> Request, const FAIHttpResponseDelegate OnComplete)
{
    Request->OnProcessRequestComplete().BindLambda([OnComplete](FHttpRequestPtr, FHttpResponsePtr Response, bool bSucceeded){
        int32 Code = 0;
        FString Body = TEXT("");
        if (bSucceeded && Response.IsValid())
        {
            Code = Response->GetResponseCode();
            Body = Response->GetContentAsString();
        }
        OnComplete.ExecuteIfBound(Code, Body);
    });
    Request->ProcessRequest();
}

static void AppendStringCRLF(TArray<uint8>& Out, const FString& Str)
{
    FTCHARToUTF8 Utf8(*Str);
    Out.Append((const uint8*)Utf8.Get(), Utf8.Length());
    Out.Add('\r'); Out.Add('\n');
}

FString UAIHttpClient::BuildMultipartBody(const FString& Boundary, const FString& FieldName, const FString& FilePath, TArray<uint8>& OutBinary)
{
    FString FileName = FPaths::GetCleanFilename(FilePath);
    TArray<uint8> FileBytes;
    FFileHelper::LoadFileToArray(FileBytes, *FilePath);

    // --boundary\r\n
    AppendStringCRLF(OutBinary, FString::Printf(TEXT("--%s"), *Boundary));

    // Content-Disposition
    AppendStringCRLF(OutBinary, FString::Printf(TEXT("Content-Disposition: form-data; name=\"%s\"; filename=\"%s\""), *FieldName, *FileName));
    // naive type sniff
    FString Mime = TEXT("application/octet-stream");
    if (FileName.EndsWith(TEXT(".wav"))) Mime = TEXT("audio/wav");
    else if (FileName.EndsWith(TEXT(".mp3"))) Mime = TEXT("audio/mpeg");
    else if (FileName.EndsWith(TEXT(".webm"))) Mime = TEXT("video/webm");
    AppendStringCRLF(OutBinary, FString::Printf(TEXT("Content-Type: %s"), *Mime));
    AppendStringCRLF(OutBinary, TEXT(""));
    // file bytes
    OutBinary.Append(FileBytes);
    OutBinary.Add('\r'); OutBinary.Add('\n');

    // closing boundary
    AppendStringCRLF(OutBinary, FString::Printf(TEXT("--%s--"), *Boundary));

    return FString();
}
